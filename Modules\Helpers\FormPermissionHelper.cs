using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using ProManage.Modules.Models.PermissionManagementForm;
using ProManage.Modules.Services;

namespace ProManage.Modules.Helpers
{
    /// <summary>
    /// Helper class for form-level permission checking and enforcement
    /// Provides centralized permission validation for individual forms
    /// </summary>
    public static class FormPermissionHelper
    {
        #region Private Fields

        private static PermissionService _permissionService;
        private static Dictionary<string, EffectivePermission> _permissionCache;
        private static int _currentUserId = 0;

        #endregion

        #region Initialization

        /// <summary>
        /// Initialize the permission helper with user context
        /// </summary>
        /// <param name="userId">Current user ID</param>
        public static void Initialize(int userId)
        {
            try
            {
                _currentUserId = userId;
                _permissionService = new PermissionService();
                RefreshPermissionCache();
                
                Debug.WriteLine($"FormPermissionHelper initialized for user ID: {userId}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error initializing FormPermissionHelper: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Refresh the permission cache
        /// </summary>
        public static void RefreshPermissionCache()
        {
            try
            {
                if (_permissionService == null || _currentUserId <= 0)
                {
                    _permissionCache = new Dictionary<string, EffectivePermission>();
                    return;
                }

                var userPermissions = _permissionService.GetUserPermissions(_currentUserId);
                _permissionCache = userPermissions?.ToDictionary(p => p.FormName, p => p) 
                    ?? new Dictionary<string, EffectivePermission>();
                
                Debug.WriteLine($"Permission cache refreshed with {_permissionCache.Count} entries");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error refreshing permission cache: {ex.Message}");
                _permissionCache = new Dictionary<string, EffectivePermission>();
            }
        }

        #endregion

        #region Permission Checking Methods

        /// <summary>
        /// Check if user has permission for a specific operation on a form
        /// </summary>
        /// <param name="formName">Name of the form</param>
        /// <param name="operation">Type of operation</param>
        /// <returns>True if user has permission</returns>
        public static bool HasPermission(string formName, PermissionOperation operation)
        {
            try
            {
                if (_permissionCache == null || !_permissionCache.ContainsKey(formName))
                {
                    Debug.WriteLine($"No permission found for form: {formName}");
                    return false;
                }

                var permission = _permissionCache[formName];
                
                return operation switch
                {
                    PermissionOperation.Read => permission.ReadPermission,
                    PermissionOperation.New => permission.NewPermission,
                    PermissionOperation.Edit => permission.EditPermission,
                    PermissionOperation.Delete => permission.DeletePermission,
                    PermissionOperation.Print => permission.PrintPermission,
                    _ => false
                };
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error checking permission: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Get the effective permission for a form
        /// </summary>
        /// <param name="formName">Name of the form</param>
        /// <returns>Effective permission or null if not found</returns>
        public static EffectivePermission GetFormPermission(string formName)
        {
            try
            {
                return _permissionCache?.ContainsKey(formName) == true ? _permissionCache[formName] : null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting form permission: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Validate permission for an operation and show error message if denied
        /// </summary>
        /// <param name="formName">Name of the form</param>
        /// <param name="operation">Type of operation</param>
        /// <param name="showMessage">Whether to show error message</param>
        /// <returns>True if permission is granted</returns>
        public static bool ValidatePermission(string formName, PermissionOperation operation, bool showMessage = true)
        {
            try
            {
                bool hasPermission = HasPermission(formName, operation);
                
                if (!hasPermission && showMessage)
                {
                    ShowPermissionDeniedMessage(operation);
                }
                
                return hasPermission;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error validating permission: {ex.Message}");
                if (showMessage)
                {
                    XtraMessageBox.Show($"Error checking permissions: {ex.Message}", "Permission Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                return false;
            }
        }

        #endregion

        #region Form Control Methods

        /// <summary>
        /// Apply permission-based control states to a form
        /// </summary>
        /// <param name="form">The form to apply permissions to</param>
        /// <param name="formName">Name of the form for permission lookup</param>
        public static void ApplyFormPermissions(Form form, string formName)
        {
            try
            {
                var permission = GetFormPermission(formName);
                if (permission == null)
                {
                    Debug.WriteLine($"No permission found for form: {formName}");
                    return;
                }

                // Set form title with permission indicator
                UpdateFormTitle(form, permission);
                
                // Apply control states based on permissions
                ApplyControlPermissions(form, permission);
                
                Debug.WriteLine($"Form permissions applied to: {formName}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error applying form permissions: {ex.Message}");
            }
        }

        /// <summary>
        /// Update form title with permission indicators
        /// </summary>
        /// <param name="form">The form to update</param>
        /// <param name="permission">The permission information</param>
        private static void UpdateFormTitle(Form form, EffectivePermission permission)
        {
            try
            {
                var originalTitle = form.Text;
                var indicator = "";
                
                if (!permission.NewPermission && !permission.EditPermission && !permission.DeletePermission)
                {
                    indicator = " [Read Only]";
                }
                else if (!permission.DeletePermission)
                {
                    indicator = " [Limited Access]";
                }
                
                form.Text = originalTitle + indicator;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating form title: {ex.Message}");
            }
        }

        /// <summary>
        /// Apply permission-based control states
        /// </summary>
        /// <param name="form">The form containing controls</param>
        /// <param name="permission">The permission information</param>
        private static void ApplyControlPermissions(Form form, EffectivePermission permission)
        {
            try
            {
                // Set read-only mode if no edit permission
                bool isReadOnly = !permission.EditPermission && !permission.NewPermission;
                
                // Apply to all controls recursively
                ApplyControlPermissionsRecursive(form, permission, isReadOnly);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error applying control permissions: {ex.Message}");
            }
        }

        /// <summary>
        /// Recursively apply permissions to controls
        /// </summary>
        /// <param name="parent">Parent control</param>
        /// <param name="permission">Permission information</param>
        /// <param name="isReadOnly">Whether controls should be read-only</param>
        private static void ApplyControlPermissionsRecursive(Control parent, EffectivePermission permission, bool isReadOnly)
        {
            try
            {
                foreach (Control control in parent.Controls)
                {
                    // Apply to input controls
                    if (control is TextEdit textEdit)
                    {
                        textEdit.Properties.ReadOnly = isReadOnly;
                    }
                    else if (control is ComboBoxEdit comboEdit)
                    {
                        comboEdit.Properties.ReadOnly = isReadOnly;
                    }
                    else if (control is DateEdit dateEdit)
                    {
                        dateEdit.Properties.ReadOnly = isReadOnly;
                    }
                    else if (control is GridView gridView)
                    {
                        gridView.OptionsBehavior.Editable = !isReadOnly;
                        gridView.OptionsBehavior.ReadOnly = isReadOnly;
                    }
                    
                    // Recursively apply to child controls
                    if (control.HasChildren)
                    {
                        ApplyControlPermissionsRecursive(control, permission, isReadOnly);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error applying control permissions recursively: {ex.Message}");
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Show permission denied message
        /// </summary>
        /// <param name="operation">The denied operation</param>
        private static void ShowPermissionDeniedMessage(PermissionOperation operation)
        {
            try
            {
                string operationText = operation switch
                {
                    PermissionOperation.Read => "view",
                    PermissionOperation.New => "create new records",
                    PermissionOperation.Edit => "edit records",
                    PermissionOperation.Delete => "delete records",
                    PermissionOperation.Print => "print",
                    _ => "perform this operation"
                };
                
                XtraMessageBox.Show($"You do not have permission to {operationText}.", "Access Denied",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error showing permission denied message: {ex.Message}");
            }
        }

        /// <summary>
        /// Get permission summary text for a form
        /// </summary>
        /// <param name="formName">Name of the form</param>
        /// <returns>Permission summary text</returns>
        public static string GetPermissionSummary(string formName)
        {
            try
            {
                var permission = GetFormPermission(formName);
                if (permission == null)
                {
                    return "No permissions";
                }
                
                var permissions = new List<string>();
                if (permission.ReadPermission) permissions.Add("Read");
                if (permission.NewPermission) permissions.Add("New");
                if (permission.EditPermission) permissions.Add("Edit");
                if (permission.DeletePermission) permissions.Add("Delete");
                if (permission.PrintPermission) permissions.Add("Print");
                
                return string.Join(", ", permissions);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting permission summary: {ex.Message}");
                return "Error retrieving permissions";
            }
        }

        #endregion
    }

    #region Enums

    /// <summary>
    /// Types of permission operations
    /// </summary>
    public enum PermissionOperation
    {
        Read,
        New,
        Edit,
        Delete,
        Print
    }

    #endregion
}
