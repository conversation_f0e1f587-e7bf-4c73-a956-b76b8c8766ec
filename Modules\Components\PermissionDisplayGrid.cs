using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Columns;
using ProManage.Modules.Models.PermissionManagementForm;
using ProManage.Modules.Services;

namespace ProManage.Modules.Components
{
    /// <summary>
    /// Reusable permission display grid control for showing permissions throughout the application
    /// Provides consistent permission visualization with color coding and event handling
    /// </summary>
    [ToolboxItem(true)]
    [ToolboxBitmap(typeof(PermissionDisplayGrid))]
    [Description("Reusable permission display grid control with color coding")]
    [Category("ProManage Controls")]
    public partial class PermissionDisplayGrid : UserControl
    {
        #region Private Fields

        private GridControl gridControl;
        private GridView gridView;
        private List<EffectivePermission> _permissions;
        private bool _showSourceIndicators = true;
        private bool _allowEditing = false;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets or sets whether to show permission source indicators (role vs override)
        /// </summary>
        public bool ShowSourceIndicators
        {
            get => _showSourceIndicators;
            set
            {
                _showSourceIndicators = value;
                RefreshDisplay();
            }
        }

        /// <summary>
        /// Gets or sets whether the grid allows editing
        /// </summary>
        public bool AllowEditing
        {
            get => _allowEditing;
            set
            {
                _allowEditing = value;
                if (gridView != null)
                {
                    gridView.OptionsBehavior.Editable = value;
                }
            }
        }

        /// <summary>
        /// Gets or sets the permissions to display
        /// </summary>
        public List<EffectivePermission> Permissions
        {
            get => _permissions;
            set
            {
                _permissions = value;
                RefreshDisplay();
            }
        }

        #endregion

        #region Events

        /// <summary>
        /// Event fired when a permission value changes
        /// </summary>
        public event EventHandler<PermissionChangedEventArgs> PermissionChanged;

        /// <summary>
        /// Event fired when permission validation is needed
        /// </summary>
        public event EventHandler<PermissionValidationEventArgs> PermissionValidating;

        #endregion

        #region Constructor

        public PermissionDisplayGrid()
        {
            InitializeComponent();
            InitializeGrid();
        }

        #endregion

        #region Initialization

        /// <summary>
        /// Initialize the grid control and view
        /// </summary>
        private void InitializeGrid()
        {
            try
            {
                // Create grid control
                gridControl = new GridControl();
                gridView = new GridView();
                
                gridControl.MainView = gridView;
                gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] { gridView });
                
                // Configure grid
                gridControl.Dock = DockStyle.Fill;
                gridView.GridControl = gridControl;
                
                // Configure grid view options
                gridView.OptionsView.ShowGroupPanel = false;
                gridView.OptionsView.ShowFilterPanelMode = DevExpress.XtraGrid.Views.Base.ShowFilterPanelMode.Never;
                gridView.OptionsSelection.MultiSelect = false;
                gridView.OptionsSelection.MultiSelectMode = DevExpress.XtraGrid.Views.Grid.GridMultiSelectMode.RowSelect;
                gridView.OptionsBehavior.Editable = _allowEditing;
                
                // Create columns
                CreateColumns();
                
                // Wire up events
                gridView.CellValueChanged += GridView_CellValueChanged;
                gridView.ValidatingEditor += GridView_ValidatingEditor;
                gridView.CustomRowCellStyle += GridView_CustomRowCellStyle;
                
                // Add to control
                this.Controls.Add(gridControl);
                
                System.Diagnostics.Debug.WriteLine("PermissionDisplayGrid initialized successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing PermissionDisplayGrid: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Create grid columns
        /// </summary>
        private void CreateColumns()
        {
            try
            {
                gridView.Columns.Clear();
                
                // Form Name column
                var colFormName = gridView.Columns.Add();
                colFormName.FieldName = "FormName";
                colFormName.Caption = "Form Name";
                colFormName.Width = 150;
                colFormName.OptionsColumn.AllowEdit = false;
                
                // Permission columns
                var colRead = gridView.Columns.Add();
                colRead.FieldName = "ReadPermission";
                colRead.Caption = "Read";
                colRead.Width = 60;
                colRead.ColumnEdit = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
                
                var colNew = gridView.Columns.Add();
                colNew.FieldName = "NewPermission";
                colNew.Caption = "New";
                colNew.Width = 60;
                colNew.ColumnEdit = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
                
                var colEdit = gridView.Columns.Add();
                colEdit.FieldName = "EditPermission";
                colEdit.Caption = "Edit";
                colEdit.Width = 60;
                colEdit.ColumnEdit = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
                
                var colDelete = gridView.Columns.Add();
                colDelete.FieldName = "DeletePermission";
                colDelete.Caption = "Delete";
                colDelete.Width = 60;
                colDelete.ColumnEdit = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
                
                var colPrint = gridView.Columns.Add();
                colPrint.FieldName = "PrintPermission";
                colPrint.Caption = "Print";
                colPrint.Width = 60;
                colPrint.ColumnEdit = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
                
                // Source column (if enabled)
                if (_showSourceIndicators)
                {
                    var colSource = gridView.Columns.Add();
                    colSource.FieldName = "Source";
                    colSource.Caption = "Source";
                    colSource.Width = 80;
                    colSource.OptionsColumn.AllowEdit = false;
                }
                
                System.Diagnostics.Debug.WriteLine("PermissionDisplayGrid columns created successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating PermissionDisplayGrid columns: {ex.Message}");
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Refresh the display with current permissions
        /// </summary>
        public void RefreshDisplay()
        {
            try
            {
                if (gridControl != null && _permissions != null)
                {
                    gridControl.DataSource = _permissions;
                    gridView.BestFitColumns();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error refreshing PermissionDisplayGrid: {ex.Message}");
            }
        }

        /// <summary>
        /// Get the currently selected permission
        /// </summary>
        /// <returns>Selected permission or null</returns>
        public EffectivePermission GetSelectedPermission()
        {
            try
            {
                if (gridView != null && gridView.FocusedRowHandle >= 0)
                {
                    return gridView.GetRow(gridView.FocusedRowHandle) as EffectivePermission;
                }
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting selected permission: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Clear all permissions
        /// </summary>
        public void ClearPermissions()
        {
            try
            {
                _permissions = null;
                if (gridControl != null)
                {
                    gridControl.DataSource = null;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error clearing permissions: {ex.Message}");
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handle cell value changes
        /// </summary>
        private void GridView_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            try
            {
                var permission = gridView.GetRow(e.RowHandle) as EffectivePermission;
                if (permission != null)
                {
                    var args = new PermissionChangedEventArgs
                    {
                        Permission = permission,
                        FieldName = e.Column.FieldName,
                        OldValue = e.Value,
                        NewValue = gridView.GetRowCellValue(e.RowHandle, e.Column)
                    };
                    
                    PermissionChanged?.Invoke(this, args);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error handling cell value change: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle editor validation
        /// </summary>
        private void GridView_ValidatingEditor(object sender, DevExpress.XtraEditors.Controls.BaseContainerValidateEditorEventArgs e)
        {
            try
            {
                var permission = gridView.GetFocusedRow() as EffectivePermission;
                if (permission != null)
                {
                    var args = new PermissionValidationEventArgs
                    {
                        Permission = permission,
                        FieldName = gridView.FocusedColumn.FieldName,
                        Value = e.Value,
                        IsValid = true,
                        ErrorMessage = string.Empty
                    };
                    
                    PermissionValidating?.Invoke(this, args);
                    
                    if (!args.IsValid)
                    {
                        e.Valid = false;
                        e.ErrorText = args.ErrorMessage;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error validating editor: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle custom row cell styling for color coding
        /// </summary>
        private void GridView_CustomRowCellStyle(object sender, DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs e)
        {
            try
            {
                if (_showSourceIndicators && e.RowHandle >= 0)
                {
                    var permission = gridView.GetRow(e.RowHandle) as EffectivePermission;
                    if (permission != null)
                    {
                        // Color coding based on permission source
                        switch (permission.Source)
                        {
                            case PermissionSource.Role:
                                e.Appearance.BackColor = Color.LightGreen;
                                break;
                            case PermissionSource.UserOverride:
                                e.Appearance.BackColor = Color.LightBlue;
                                break;
                            default:
                                e.Appearance.BackColor = Color.White;
                                break;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error applying custom row style: {ex.Message}");
            }
        }

        #endregion
    }

    #region Event Args Classes

    /// <summary>
    /// Event arguments for permission changed events
    /// </summary>
    public class PermissionChangedEventArgs : EventArgs
    {
        public EffectivePermission Permission { get; set; }
        public string FieldName { get; set; }
        public object OldValue { get; set; }
        public object NewValue { get; set; }
    }

    /// <summary>
    /// Event arguments for permission validation events
    /// </summary>
    public class PermissionValidationEventArgs : EventArgs
    {
        public EffectivePermission Permission { get; set; }
        public string FieldName { get; set; }
        public object Value { get; set; }
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; }
    }

    #endregion
}
