using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using ProManage.Modules.Models.PermissionManagementForm;

namespace ProManage.Modules.Components
{
    /// <summary>
    /// Permission status indicator with visual feedback for permission states
    /// Provides color-coded visual indicators for different permission levels
    /// </summary>
    [ToolboxItem(true)]
    [ToolboxBitmap(typeof(PermissionStatusIndicator))]
    [Description("Permission status indicator with visual feedback")]
    [Category("ProManage Controls")]
    public partial class PermissionStatusIndicator : UserControl
    {
        #region Private Fields

        private LabelControl lblStatus;
        private PictureEdit picIcon;
        private PermissionLevel _permissionLevel = PermissionLevel.None;
        private string _permissionText = "No Permission";
        private bool _showIcon = true;
        private bool _showText = true;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets or sets the permission level to display
        /// </summary>
        [Category("Permission")]
        [Description("The permission level to display")]
        public PermissionLevel PermissionLevel
        {
            get => _permissionLevel;
            set
            {
                _permissionLevel = value;
                UpdateDisplay();
            }
        }

        /// <summary>
        /// Gets or sets the permission text to display
        /// </summary>
        [Category("Permission")]
        [Description("The permission text to display")]
        public string PermissionText
        {
            get => _permissionText;
            set
            {
                _permissionText = value;
                UpdateDisplay();
            }
        }

        /// <summary>
        /// Gets or sets whether to show the icon
        /// </summary>
        [Category("Appearance")]
        [Description("Whether to show the permission icon")]
        public bool ShowIcon
        {
            get => _showIcon;
            set
            {
                _showIcon = value;
                UpdateDisplay();
            }
        }

        /// <summary>
        /// Gets or sets whether to show the text
        /// </summary>
        [Category("Appearance")]
        [Description("Whether to show the permission text")]
        public bool ShowText
        {
            get => _showText;
            set
            {
                _showText = value;
                UpdateDisplay();
            }
        }

        #endregion

        #region Events

        /// <summary>
        /// Event fired when the permission status is clicked
        /// </summary>
        public event EventHandler StatusClicked;

        #endregion

        #region Constructor

        public PermissionStatusIndicator()
        {
            InitializeComponent();
            InitializeControls();
        }

        #endregion

        #region Initialization

        /// <summary>
        /// Initialize the controls
        /// </summary>
        private void InitializeControls()
        {
            try
            {
                // Set control properties
                this.Size = new Size(120, 24);
                this.BackColor = Color.Transparent;
                
                // Create picture edit for icon
                picIcon = new PictureEdit();
                picIcon.Properties.ShowCameraMenuItem = DevExpress.XtraEditors.Controls.CameraMenuItemVisibility.Never;
                picIcon.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Zoom;
                picIcon.Properties.ShowMenu = false;
                picIcon.Properties.ReadOnly = true;
                picIcon.Size = new Size(16, 16);
                picIcon.Location = new Point(2, 4);
                picIcon.Click += Control_Click;
                
                // Create label for text
                lblStatus = new LabelControl();
                lblStatus.AutoSizeMode = LabelAutoSizeMode.None;
                lblStatus.Location = new Point(22, 4);
                lblStatus.Size = new Size(96, 16);
                lblStatus.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
                lblStatus.Click += Control_Click;
                
                // Add controls
                this.Controls.Add(picIcon);
                this.Controls.Add(lblStatus);
                
                // Initial display update
                UpdateDisplay();
                
                System.Diagnostics.Debug.WriteLine("PermissionStatusIndicator initialized successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing PermissionStatusIndicator: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Set permission status from effective permission
        /// </summary>
        /// <param name="permission">The effective permission to display</param>
        public void SetPermission(EffectivePermission permission)
        {
            try
            {
                if (permission == null)
                {
                    PermissionLevel = PermissionLevel.None;
                    PermissionText = "No Permission";
                    return;
                }

                // Determine permission level based on effective permissions
                if (permission.ReadPermission && permission.NewPermission && 
                    permission.EditPermission && permission.DeletePermission && permission.PrintPermission)
                {
                    PermissionLevel = PermissionLevel.Full;
                    PermissionText = "Full Access";
                }
                else if (permission.ReadPermission && (permission.NewPermission || permission.EditPermission))
                {
                    PermissionLevel = PermissionLevel.ReadWrite;
                    PermissionText = "Read/Write";
                }
                else if (permission.ReadPermission)
                {
                    PermissionLevel = PermissionLevel.ReadOnly;
                    PermissionText = "Read Only";
                }
                else
                {
                    PermissionLevel = PermissionLevel.None;
                    PermissionText = "No Access";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error setting permission: {ex.Message}");
            }
        }

        /// <summary>
        /// Update the visual display
        /// </summary>
        public void UpdateDisplay()
        {
            try
            {
                if (lblStatus == null || picIcon == null) return;

                // Update text
                if (_showText)
                {
                    lblStatus.Text = _permissionText;
                    lblStatus.Visible = true;
                }
                else
                {
                    lblStatus.Visible = false;
                }

                // Update icon and colors based on permission level
                if (_showIcon)
                {
                    picIcon.Visible = true;
                    UpdateIconAndColors();
                }
                else
                {
                    picIcon.Visible = false;
                }

                // Adjust layout
                AdjustLayout();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating display: {ex.Message}");
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Update icon and colors based on permission level
        /// </summary>
        private void UpdateIconAndColors()
        {
            try
            {
                Color backColor;
                Color textColor;
                Image icon = null;

                switch (_permissionLevel)
                {
                    case PermissionLevel.Full:
                        backColor = Color.LightGreen;
                        textColor = Color.DarkGreen;
                        icon = CreateStatusIcon(Color.Green);
                        break;
                    case PermissionLevel.ReadWrite:
                        backColor = Color.LightBlue;
                        textColor = Color.DarkBlue;
                        icon = CreateStatusIcon(Color.Blue);
                        break;
                    case PermissionLevel.ReadOnly:
                        backColor = Color.LightYellow;
                        textColor = Color.DarkOrange;
                        icon = CreateStatusIcon(Color.Orange);
                        break;
                    case PermissionLevel.None:
                    default:
                        backColor = Color.LightGray;
                        textColor = Color.DarkGray;
                        icon = CreateStatusIcon(Color.Gray);
                        break;
                }

                // Apply colors
                this.BackColor = backColor;
                lblStatus.Appearance.ForeColor = textColor;
                picIcon.Image = icon;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating icon and colors: {ex.Message}");
            }
        }

        /// <summary>
        /// Create a status icon with the specified color
        /// </summary>
        /// <param name="color">The color for the icon</param>
        /// <returns>The created icon image</returns>
        private Image CreateStatusIcon(Color color)
        {
            try
            {
                var bitmap = new Bitmap(16, 16);
                using (var g = Graphics.FromImage(bitmap))
                {
                    g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
                    
                    // Draw circle
                    using (var brush = new SolidBrush(color))
                    {
                        g.FillEllipse(brush, 2, 2, 12, 12);
                    }
                    
                    // Draw border
                    using (var pen = new Pen(Color.Black, 1))
                    {
                        g.DrawEllipse(pen, 2, 2, 12, 12);
                    }
                }
                return bitmap;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating status icon: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Adjust the layout of controls
        /// </summary>
        private void AdjustLayout()
        {
            try
            {
                int currentX = 2;
                
                if (_showIcon && picIcon.Visible)
                {
                    picIcon.Location = new Point(currentX, 4);
                    currentX += picIcon.Width + 4;
                }
                
                if (_showText && lblStatus.Visible)
                {
                    lblStatus.Location = new Point(currentX, 4);
                    lblStatus.Size = new Size(this.Width - currentX - 2, 16);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error adjusting layout: {ex.Message}");
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handle control click events
        /// </summary>
        private void Control_Click(object sender, EventArgs e)
        {
            try
            {
                StatusClicked?.Invoke(this, e);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error handling control click: {ex.Message}");
            }
        }

        #endregion
    }

    #region Enums

    /// <summary>
    /// Permission levels for visual indication
    /// </summary>
    public enum PermissionLevel
    {
        None,
        ReadOnly,
        ReadWrite,
        Full
    }

    #endregion
}
